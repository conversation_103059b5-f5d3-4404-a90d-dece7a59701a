<?php

namespace App\Http\Controllers;

use App\Models\KategoriKapal;
use Illuminate\Http\Request;

class KategoriKapalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $kategoriKapals = KategoriKapal::orderBy('nama_kategori')->get();
        return view('kategori-kapal.index', compact('kategoriKapals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('kategori-kapal.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_kategori' => 'required|string|max:255|unique:kategori_kapals',
            'deskripsi' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        KategoriKapal::create($request->all());

        return redirect()->route('kategori-kapal.index')
            ->with('success', 'Kategori kapal berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(KategoriKapal $kategoriKapal)
    {
        $kategoriKapal->load('kapals');
        return view('kategori-kapal.show', compact('kategoriKapal'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(KategoriKapal $kategoriKapal)
    {
        return view('kategori-kapal.edit', compact('kategoriKapal'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, KategoriKapal $kategoriKapal)
    {
        $request->validate([
            'nama_kategori' => 'required|string|max:255|unique:kategori_kapals,nama_kategori,' . $kategoriKapal->id,
            'deskripsi' => 'nullable|string',
            'status' => 'required|in:active,inactive',
        ]);

        $kategoriKapal->update($request->all());

        return redirect()->route('kategori-kapal.index')
            ->with('success', 'Kategori kapal berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(KategoriKapal $kategoriKapal)
    {
        // Check if category has ships
        if ($kategoriKapal->kapals()->count() > 0) {
            return redirect()->route('kategori-kapal.index')
                ->with('error', 'Kategori kapal tidak dapat dihapus karena masih memiliki kapal terkait.');
        }

        $kategoriKapal->delete();

        return redirect()->route('kategori-kapal.index')
            ->with('success', 'Kategori kapal berhasil dihapus.');
    }
}
