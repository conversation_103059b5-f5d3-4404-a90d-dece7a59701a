@extends('layouts.app')

@section('title', 'Master Tarif - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Master Tarif</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Master Tarif</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Manajemen Master Tarif</h5>
                            <p class="card-subtitle mb-0">DataTables untuk mengelola tarif berbagai jenis layanan pelabuhan
                                dengan fitur pencarian, sorting, dan pagination. Anda dapat merujuk dokumentasi lengkap dari
                                <a href="https://datatables.net/">DataTables</a>
                            </p>
                        </div>
                        <div>
                            <a href="{{ route('tarif.create') }}" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Tarif
                            </a>
                        </div>
                    </div>

                    @if (session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if (session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <!-- Filter by Rate Type -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="filterJenis" onchange="filterByJenis()">
                                <option value="">Semua Jenis Tarif</option>
                                @foreach ($jenisTarif as $key => $label)
                                    <option value="{{ $key }}" {{ request('jenis') == $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="tarif_datatable" class="table border table-striped table-bordered text-nowrap">
                            <thead>
                                <tr>
                                    <th>Nama Tarif</th>
                                    <th>Jenis</th>
                                    <th>Kategori Kapal</th>
                                    <th>Satuan</th>
                                    <th>Harga</th>
                                    <th>Status</th>
                                    <th style="width: 120px;">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($tarifs as $tarif)
                                    <tr>
                                        <td>
                                            <h6 class="fw-semibold mb-0">{{ $tarif->nama_tarif }}</h6>
                                        </td>
                                        <td>
                                            <span
                                                class="badge
                                            @if ($tarif->jenis_tarif == 'tambat') bg-primary
                                            @elseif($tarif->jenis_tarif == 'tambat_sementara') bg-success
                                            @elseif($tarif->jenis_tarif == 'tambat_tetap') bg-info
                                            @elseif($tarif->jenis_tarif == 'tambat_labuh') bg-warning
                                            @elseif($tarif->jenis_tarif == 'perbaikan_produksi') bg-dark
                                            @elseif($tarif->jenis_tarif == 'bongkar') bg-danger
                                            @elseif($tarif->jenis_tarif == 'muat') bg-info
                                            @else bg-secondary @endif rounded-3 fw-semibold">
                                                {{ $tarif->jenis_tarif_label }}
                                            </span>
                                        </td>
                                        <td>
                                            @if ($tarif->kategoriKapals->count() > 0)
                                                @foreach ($tarif->kategoriKapals as $kategori)
                                                    <span
                                                        class="badge bg-info rounded-3 fw-semibold me-1 mb-1">{{ $kategori->nama_kategori }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">Semua Kategori</span>
                                            @endif
                                        </td>
                                        <td>{{ $tarif->satuan }}</td>
                                        <td>{{ $tarif->formatted_harga }}</td>
                                        <td>
                                            @if ($tarif->status == 'active')
                                                <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                            @else
                                                <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-2">
                                                <a href="{{ route('tarif.show', $tarif) }}"
                                                    class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('tarif.edit', $tarif) }}"
                                                    class="btn btn-outline-warning btn-sm" title="Edit">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                <form action="{{ route('tarif.destroy', $tarif) }}" method="POST"
                                                    class="d-inline"
                                                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus tarif ini?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        title="Hapus">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            $('#tarif_datatable').DataTable({
                "pageLength": 10,
                "lengthMenu": [5, 10, 25, 50, 100],
                "scrollX": true,
                "autoWidth": false,
                "language": {
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data yang tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "search": "Cari:",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                },
                "columnDefs": [{
                    "targets": [5], // Kolom Aksi
                    "orderable": false,
                    "searchable": false,
                    "width": "120px"
                }],
                "order": [
                    [1, 'asc'],
                    [0, 'asc']
                ] // Default sort by jenis then nama
            });
        });

        function filterByJenis() {
            const jenis = document.getElementById('filterJenis').value;
            const url = new URL(window.location);
            if (jenis) {
                url.searchParams.set('jenis', jenis);
            } else {
                url.searchParams.delete('jenis');
            }
            window.location = url;
        }
    </script>
@endpush
