<?php $__env->startSection('title', 'Buat Retribusi - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Buat Retribusi Baru</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted"
                                    href="<?php echo e(route('petugas.dashboard')); ?>">Dashboard</a></li>
                            <li class="breadcrumb-item" aria-current="page">Buat Retribusi</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <h5 class="card-title fw-semibold">Form Retribusi</h5>
            <p class="card-subtitle mb-4">Isi form di bawah untuk membuat retribusi baru</p>

            <form action="<?php echo e(route('petugas.retribusi.store')); ?>" method="POST" id="retribusiForm">
                <?php echo csrf_field(); ?>

                <!-- Step 1: Jenis Pembayar -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">Jenis Pembayar <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_pembayar" id="pribadi"
                                            value="pribadi" <?php echo e(old('jenis_pembayar') == 'pribadi' ? 'checked' : ''); ?>

                                            required>
                                        <label class="form-check-label" for="pribadi">
                                            <strong>Pribadi</strong><br>
                                            <small class="text-muted">Pembayaran atas nama pribadi</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="jenis_pembayar" id="perusahaan"
                                            value="perusahaan" <?php echo e(old('jenis_pembayar') == 'perusahaan' ? 'checked' : ''); ?>

                                            required>
                                        <label class="form-check-label" for="perusahaan">
                                            <strong>Perusahaan</strong><br>
                                            <small class="text-muted">Pembayaran atas nama perusahaan</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <?php $__errorArgs = ['jenis_pembayar'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Company Selection (only for perusahaan) -->
                <div class="row" id="companySection" style="display: none;">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="company_id" class="form-label">Pilih Perusahaan <span
                                    class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['company_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="company_id"
                                name="company_id">
                                <option value="">Pilih Perusahaan</option>
                                <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($company->id); ?>"
                                        <?php echo e(old('company_id') == $company->id ? 'selected' : ''); ?>>
                                        <?php echo e($company->nama_perusahaan); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['company_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Kapal Selection -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kapal_id" class="form-label">Pilih Kapal <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['kapal_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="kapal_id"
                                name="kapal_id" required>
                                <option value="">Pilih jenis pembayar terlebih dahulu</option>
                            </select>
                            <?php $__errorArgs = ['kapal_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Tarif Selection -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tarif_id" class="form-label">Pilih Tipe Tarif <span
                                    class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['tarif_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="tarif_id"
                                name="tarif_id" required>
                                <option value="">Pilih Kapal Terlebih Dahulu</option>
                            </select>
                            <?php $__errorArgs = ['tarif_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">
                                <i class="ti ti-info-circle me-1"></i>
                                Pilihan tarif akan disesuaikan dengan kategori kapal yang dipilih
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jumlah" class="form-label">Jumlah <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" step="0.01" min="0.01"
                                    class="form-control <?php $__errorArgs = ['jumlah'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="jumlah"
                                    name="jumlah" value="<?php echo e(old('jumlah')); ?>" required>
                                <span class="input-group-text" id="satuanText">-</span>
                            </div>
                            <?php $__errorArgs = ['jumlah'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Calculation Display -->
                <div class="row" id="calculationSection" style="display: none;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h6 class="mb-2">Perhitungan:</h6>
                            <p class="mb-1">Harga per <span id="displaySatuan">-</span>: <span
                                    id="displayHarga">-</span></p>
                            <p class="mb-1">Jumlah: <span id="displayJumlah">-</span> <span
                                    id="displaySatuan2">-</span></p>
                            <hr>
                            <h5 class="mb-0">Total: <span id="displayTotal" class="text-primary">-</span></h5>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Payment Method -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="metode_pembayaran_id" class="form-label">Metode Pembayaran <span
                                    class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['metode_pembayaran_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="metode_pembayaran_id" name="metode_pembayaran_id" required>
                                <option value="">Pilih Metode Pembayaran</option>
                                <?php $__currentLoopData = $metodePembayarans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $metode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($metode->id); ?>"
                                        <?php echo e(old('metode_pembayaran_id') == $metode->id ? 'selected' : ''); ?>>
                                        <?php echo e($metode->nama_metode); ?> (<?php echo e($metode->tipe_metode_label); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['metode_pembayaran_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Keterangan -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="keterangan" class="form-label">Keterangan</label>
                            <textarea class="form-control <?php $__errorArgs = ['keterangan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="keterangan" name="keterangan"
                                rows="3"><?php echo e(old('keterangan')); ?></textarea>
                            <?php $__errorArgs = ['keterangan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-2"></i>Buat Retribusi
                    </button>
                    <a href="<?php echo e(route('petugas.dashboard')); ?>" class="btn btn-outline-secondary">
                        <i class="ti ti-arrow-left me-2"></i>Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Handle jenis pembayar change
            $('input[name="jenis_pembayar"]').change(function() {
                const jenisPembayar = $(this).val();

                if (jenisPembayar === 'perusahaan') {
                    $('#companySection').show();
                    $('#company_id').prop('required', true);
                } else {
                    $('#companySection').hide();
                    $('#company_id').prop('required', false);
                    $('#company_id').val('');
                }

                // Load ships based on payment type
                loadShipsByPaymentType();
            });

            // Handle company selection change
            $('#company_id').change(function() {
                loadShipsByPaymentType();
            });

            // Handle ship selection change
            $('#kapal_id').change(function() {
                loadTarifsByShipCategory();
            });

            // Function to load ships based on payment type and company
            function loadShipsByPaymentType() {
                const jenisPembayar = $('input[name="jenis_pembayar"]:checked').val();
                const companyId = $('#company_id').val();

                if (!jenisPembayar) {
                    return;
                }

                // Clear current ship options
                $('#kapal_id').html('<option value="">Loading...</option>');

                // Prepare data for AJAX request
                const requestData = {
                    jenis_pembayar: jenisPembayar
                };

                if (jenisPembayar === 'perusahaan' && companyId) {
                    requestData.company_id = companyId;
                }

                // Make AJAX request to get ships
                $.ajax({
                    url: '<?php echo e(route('petugas.ships.by.payment.type')); ?>',
                    method: 'GET',
                    data: requestData,
                    success: function(ships) {
                        let options = '<option value="">Pilih Kapal</option>';

                        ships.forEach(function(ship) {
                            const kategoriText = ship.kategori_nama ?
                                ` - ${ship.kategori_nama}` : ' - Tanpa Kategori';
                            options +=
                                `<option value="${ship.id}">${ship.nama_kapal} (${ship.nomor_imo})${kategoriText}</option>`;
                        });

                        $('#kapal_id').html(options);
                    },
                    error: function() {
                        $('#kapal_id').html('<option value="">Error loading ships</option>');
                        console.error('Error loading ships');
                    }
                });
            }

            // Function to load tarifs based on selected ship category
            function loadTarifsByShipCategory() {
                const kapalId = $('#kapal_id').val();

                if (!kapalId) {
                    // Clear tarif options if no ship selected
                    $('#tarif_id').html('<option value="">Pilih Tipe Tarif</option>');
                    $('#calculationSection').hide();
                    return;
                }

                // Clear current tarif options
                $('#tarif_id').html('<option value="">Loading...</option>');

                // Make AJAX request to get tarifs based on ship category
                $.ajax({
                    url: '<?php echo e(route('petugas.tarifs.by.ship.category')); ?>',
                    method: 'GET',
                    data: {
                        kapal_id: kapalId
                    },
                    success: function(tarifs) {
                        let options = '<option value="">Pilih Tipe Tarif</option>';

                        tarifs.forEach(function(tarif) {
                            options += `<option value="${tarif.id}" data-harga="${tarif.harga}" data-satuan="${tarif.satuan}">
                                ${tarif.display_text}
                            </option>`;
                        });

                        $('#tarif_id').html(options);

                        // Hide calculation section when tarifs are reloaded
                        $('#calculationSection').hide();
                    },
                    error: function() {
                        $('#tarif_id').html('<option value="">Error loading tarifs</option>');
                        console.error('Error loading tarifs');
                    }
                });
            }

            // Handle tarif change
            $('#tarif_id').change(function() {
                const selectedOption = $(this).find('option:selected');
                const harga = selectedOption.data('harga');
                const satuan = selectedOption.data('satuan');

                if (harga && satuan) {
                    $('#satuanText').text(satuan);
                    $('#displayHarga').text('Rp ' + new Intl.NumberFormat('id-ID').format(harga));
                    $('#displaySatuan, #displaySatuan2').text(satuan);
                    calculateTotal();
                } else {
                    $('#satuanText').text('-');
                    $('#calculationSection').hide();
                }
            });

            // Handle jumlah change
            $('#jumlah').on('input', function() {
                calculateTotal();
            });

            function calculateTotal() {
                const selectedOption = $('#tarif_id').find('option:selected');
                const harga = selectedOption.data('harga');
                const jumlah = parseFloat($('#jumlah').val()) || 0;

                if (harga && jumlah > 0) {
                    const total = harga * jumlah;
                    $('#displayJumlah').text(jumlah);
                    $('#displayTotal').text('Rp ' + new Intl.NumberFormat('id-ID').format(total));
                    $('#calculationSection').show();
                } else {
                    $('#calculationSection').hide();
                }
            }

            // Initialize on page load
            if ($('input[name="jenis_pembayar"]:checked').val() === 'perusahaan') {
                $('#companySection').show();
            }

            if ($('#tarif_id').val()) {
                $('#tarif_id').trigger('change');
            }

            // Load ships on initial page load if payment type is already selected
            const initialPaymentType = $('input[name="jenis_pembayar"]:checked').val();
            if (initialPaymentType) {
                loadShipsByPaymentType();
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('petugas.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/petugas/retribusi/create.blade.php ENDPATH**/ ?>