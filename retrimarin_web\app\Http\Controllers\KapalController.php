<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Kapal;
use App\Models\Company;
use App\Models\KategoriKapal;
use Illuminate\Support\Facades\Hash;

class KapalController extends Controller
{
    public function index()
    {
        $kapals = Kapal::with(['company', 'kategoriKapal'])->latest()->get();
        return view('kapal.index', compact('kapals'));
    }

    public function create()
    {
        $companies = Company::active()->orderBy('nama_perusahaan')->get();
        $kategoriKapals = KategoriKapal::active()->orderBy('nama_kategori')->get();
        return view('kapal.create', compact('companies', 'kategoriKapals'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'company_id' => 'nullable|exists:companies,id',
            'kategori_kapal_id' => 'nullable|exists:kategori_kapals,id',
            'nama_kapal' => 'required|string|max:255',
            'nomor_imo' => 'required|string|unique:kapals',
            'jenis_kapal' => 'required|string|max:255',
            'bendera' => 'required|string|max:255',
            'tahun_pembuatan' => 'required|integer|min:1900|max:' . date('Y'),
            'panjang' => 'required|numeric|min:0',
            'lebar' => 'required|numeric|min:0',
            'draft' => 'required|numeric|min:0',
            'gross_tonnage' => 'required|numeric|min:0',
            'operator_username' => 'required|string|unique:kapals',
            'operator_password' => 'required|string|min:6',
            'status' => 'required|in:active,inactive,maintenance',
            'keterangan' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['operator_password'] = Hash::make($request->operator_password);

        Kapal::create($data);

        return redirect()->route('kapal.index')->with('success', 'Data kapal berhasil ditambahkan.');
    }

    public function show(Kapal $kapal)
    {
        return view('kapal.show', compact('kapal'));
    }

    public function edit(Kapal $kapal)
    {
        $companies = Company::active()->orderBy('nama_perusahaan')->get();
        $kategoriKapals = KategoriKapal::active()->orderBy('nama_kategori')->get();
        return view('kapal.edit', compact('kapal', 'companies', 'kategoriKapals'));
    }

    public function update(Request $request, Kapal $kapal)
    {
        $request->validate([
            'company_id' => 'nullable|exists:companies,id',
            'kategori_kapal_id' => 'nullable|exists:kategori_kapals,id',
            'nama_kapal' => 'required|string|max:255',
            'nomor_imo' => 'required|string|unique:kapals,nomor_imo,' . $kapal->id,
            'jenis_kapal' => 'required|string|max:255',
            'bendera' => 'required|string|max:255',
            'tahun_pembuatan' => 'required|integer|min:1900|max:' . date('Y'),
            'panjang' => 'required|numeric|min:0',
            'lebar' => 'required|numeric|min:0',
            'draft' => 'required|numeric|min:0',
            'gross_tonnage' => 'required|numeric|min:0',
            'operator_username' => 'required|string|unique:kapals,operator_username,' . $kapal->id,
            'operator_password' => 'nullable|string|min:6',
            'status' => 'required|in:active,inactive,maintenance',
            'keterangan' => 'nullable|string',
        ]);

        $data = $request->all();
        if ($request->filled('operator_password')) {
            $data['operator_password'] = Hash::make($request->operator_password);
        } else {
            unset($data['operator_password']);
        }

        $kapal->update($data);

        return redirect()->route('kapal.index')->with('success', 'Data kapal berhasil diperbarui.');
    }

    public function destroy(Kapal $kapal)
    {
        $kapal->delete();
        return redirect()->route('kapal.index')->with('success', 'Data kapal berhasil dihapus.');
    }
}
