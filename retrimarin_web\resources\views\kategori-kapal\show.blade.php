@extends('layouts.app')

@section('title', 'Detail Kategori Kapal')

@section('content')
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Kategori Kapal</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('kategori-kapal.index') }}">Kate<PERSON><PERSON>pal</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Detail</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5 class="card-title mb-0">Informasi Kategori Kapal</h5>
                            <div>
                                <a href="{{ route('kategori-kapal.edit', $kategoriKapal) }}" class="btn btn-warning btn-sm">
                                    <i class="ti ti-edit me-1"></i>Edit
                                </a>
                                <a href="{{ route('kategori-kapal.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="ti ti-arrow-left me-1"></i>Kembali
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Nama Kategori</label>
                                    <p class="form-control-static">{{ $kategoriKapal->nama_kategori }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Status</label>
                                    <p class="form-control-static">
                                        @if ($kategoriKapal->status == 'active')
                                            <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                        @else
                                            <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Deskripsi</label>
                                    <p class="form-control-static">{{ $kategoriKapal->deskripsi ?? 'Tidak ada deskripsi' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Dibuat</label>
                                    <p class="form-control-static">{{ $kategoriKapal->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Terakhir Diupdate</label>
                                    <p class="form-control-static">{{ $kategoriKapal->updated_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Statistik</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <h3 class="fw-semibold text-primary">{{ $kategoriKapal->kapals->count() }}</h3>
                            <p class="mb-0">Total Kapal dalam Kategori</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if($kategoriKapal->kapals->count() > 0)
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Daftar Kapal dalam Kategori</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nama Kapal</th>
                                        <th>Perusahaan</th>
                                        <th>IMO</th>
                                        <th>Jenis</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($kategoriKapal->kapals as $kapal)
                                    <tr>
                                        <td>{{ $kapal->nama_kapal }}</td>
                                        <td>{{ $kapal->company ? $kapal->company->nama_perusahaan : 'Milik Pribadi' }}</td>
                                        <td>{{ $kapal->nomor_imo }}</td>
                                        <td>{{ $kapal->jenis_kapal }}</td>
                                        <td>
                                            @if ($kapal->status == 'active')
                                                <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                            @elseif($kapal->status == 'maintenance')
                                                <span class="badge bg-warning rounded-3 fw-semibold">Maintenance</span>
                                            @else
                                                <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('kapal.show', $kapal) }}" class="btn btn-outline-primary btn-sm">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
@endsection
