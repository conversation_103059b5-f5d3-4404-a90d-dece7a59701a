@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8"><PERSON><PERSON><PERSON></h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Kate<PERSON><PERSON></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                            <div class="mb-3 mb-sm-0">
                                <h5 class="card-title fw-semibold">Manajemen Kategori Kapal</h5>
                                <p class="card-subtitle mb-0">DataTables untuk mengelola kategori kapal seperti Besar, Kecil, Sedang, dll
                                    dengan fitur pencarian, sorting, dan pagination. Anda dapat merujuk dokumentasi lengkap dari
                                    <a href="https://datatables.net/">DataTables</a>
                                </p>
                            </div>
                            <div>
                                <a href="{{ route('kategori-kapal.create') }}" class="btn btn-primary">
                                    <i class="ti ti-plus me-2"></i>Tambah Kategori
                                </a>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table id="kategori_kapal_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Nama Kategori</th>
                                        <th>Deskripsi</th>
                                        <th>Jumlah Kapal</th>
                                        <th>Status</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($kategoriKapals as $kategori)
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0">{{ $kategori->nama_kategori }}</h6>
                                            </td>
                                            <td>{{ $kategori->deskripsi ?? '-' }}</td>
                                            <td>
                                                <span class="badge bg-primary rounded-3 fw-semibold">
                                                    {{ $kategori->kapals_count ?? $kategori->kapals()->count() }} Kapal
                                                </span>
                                            </td>
                                            <td>
                                                @if ($kategori->status == 'active')
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                @else
                                                    <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <a href="{{ route('kategori-kapal.show', $kategori) }}"
                                                        class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="{{ route('kategori-kapal.edit', $kategori) }}"
                                                        class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="{{ route('kategori-kapal.destroy', $kategori) }}" method="POST"
                                                        class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus kategori ini?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            $('#kategori_kapal_datatable').DataTable({
                "pageLength": 25,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                }
            });
        });
    </script>
@endsection
