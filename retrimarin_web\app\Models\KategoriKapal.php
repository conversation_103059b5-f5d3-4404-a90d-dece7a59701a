<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KategoriKapal extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_kategori',
        'deskripsi',
        'status',
    ];

    /**
     * Get the ships that belong to this category.
     */
    public function kapals()
    {
        return $this->hasMany(Kapal::class);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the tarifs that apply to this ship category.
     */
    public function tarifs()
    {
        return $this->belongsToMany(Tarif::class, 'tarif_kategori_kapal');
    }
}
