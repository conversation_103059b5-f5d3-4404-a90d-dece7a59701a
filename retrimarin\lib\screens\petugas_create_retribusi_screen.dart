import 'package:flutter/material.dart';
import '../services/petugas_api_service.dart';

class PetugasCreateRetribusiScreen extends StatefulWidget {
  const PetugasCreateRetribusiScreen({super.key});

  @override
  State<PetugasCreateRetribusiScreen> createState() =>
      _PetugasCreateRetribusiScreenState();
}

class _PetugasCreateRetribusiScreenState
    extends State<PetugasCreateRetribusiScreen> {
  final _formKey = GlobalKey<FormState>();
  final PetugasApiService _apiService = PetugasApiService();

  // Form controllers
  final _jumlahController = TextEditingController();
  final _keteranganController = TextEditingController();

  // Form data
  String? _jenisPembayar;
  int? _companyId;
  int? _kapalId;
  int? _tarifId;
  int? _metodePembayaranId;

  // Dropdown data
  List<Map<String, dynamic>> _companies = [];
  List<Map<String, dynamic>> _ships = [];
  List<Map<String, dynamic>> _tarifs = [];
  List<Map<String, dynamic>> _paymentMethods = [];

  // Loading states
  bool _isLoading = false;
  bool _isLoadingShips = false;
  bool _isLoadingTarifs = false;

  // Selected data for display
  Map<String, dynamic>? _selectedTarif;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  @override
  void dispose() {
    _jumlahController.dispose();
    _keteranganController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final companies = await _apiService.getCompanies();
      final paymentMethods = await _apiService.getPaymentMethods();

      setState(() {
        _companies = companies;
        _paymentMethods = paymentMethods;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Gagal memuat data: $e')));
      }
    }
  }

  Future<void> _loadShips() async {
    if (_jenisPembayar == null) return;

    setState(() {
      _isLoadingShips = true;
      _ships = [];
      _kapalId = null;
      _tarifs = [];
      _tarifId = null;
      _selectedTarif = null;
    });

    try {
      final ships = await _apiService.getShipsByPaymentType(
        jenisPembayar: _jenisPembayar!,
        companyId: _companyId,
      );

      setState(() {
        _ships = ships;
        _isLoadingShips = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingShips = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Gagal memuat data kapal: $e')));
      }
    }
  }

  Future<void> _loadTarifs() async {
    if (_kapalId == null) return;

    setState(() {
      _isLoadingTarifs = true;
      _tarifs = [];
      _tarifId = null;
      _selectedTarif = null;
    });

    try {
      final tarifs = await _apiService.getTarifsByShipCategory(
        kapalId: _kapalId!,
      );

      setState(() {
        _tarifs = tarifs;
        _isLoadingTarifs = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingTarifs = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Gagal memuat data tarif: $e')));
      }
    }
  }

  Future<void> _submitRetribusi() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _apiService.createRetribusi(
        jenisPembayar: _jenisPembayar!,
        companyId: _companyId,
        kapalId: _kapalId!,
        tarifId: _tarifId!,
        jumlah: double.parse(_jumlahController.text),
        metodePembayaranId: _metodePembayaranId!,
        keterangan:
            _keteranganController.text.isNotEmpty
                ? _keteranganController.text
                : null,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Retribusi berhasil dibuat'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Gagal membuat retribusi: $e')));
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  double get _totalBayar {
    if (_selectedTarif == null || _jumlahController.text.isEmpty) return 0;
    final jumlah = double.tryParse(_jumlahController.text) ?? 0;
    final harga = _selectedTarif!['harga'] ?? 0;
    return jumlah * harga;
  }

  String _formatCurrency(double amount) {
    return amount
        .toStringAsFixed(0)
        .replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        title: const Text(
          'Buat Retribusi',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Jenis Pembayar Card
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Jenis Pembayar',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(height: 12),
                              DropdownButtonFormField<String>(
                                value: _jenisPembayar,
                                decoration: const InputDecoration(
                                  labelText: 'Pilih Jenis Pembayar',
                                  border: OutlineInputBorder(),
                                ),
                                items: const [
                                  DropdownMenuItem(
                                    value: 'pribadi',
                                    child: Text('Pribadi'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'perusahaan',
                                    child: Text('Perusahaan'),
                                  ),
                                ],
                                onChanged: (value) {
                                  setState(() {
                                    _jenisPembayar = value;
                                    _companyId = null;
                                  });
                                  _loadShips();
                                },
                                validator:
                                    (value) =>
                                        value == null
                                            ? 'Pilih jenis pembayar'
                                            : null,
                              ),
                              if (_jenisPembayar == 'perusahaan') ...[
                                const SizedBox(height: 16),
                                DropdownButtonFormField<int>(
                                  value: _companyId,
                                  decoration: const InputDecoration(
                                    labelText: 'Pilih Perusahaan',
                                    border: OutlineInputBorder(),
                                  ),
                                  items:
                                      _companies.map((company) {
                                        return DropdownMenuItem<int>(
                                          value: company['id'],
                                          child: Text(
                                            company['nama_perusahaan'],
                                          ),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _companyId = value;
                                    });
                                    _loadShips();
                                  },
                                  validator:
                                      (value) =>
                                          _jenisPembayar == 'perusahaan' &&
                                                  value == null
                                              ? 'Pilih perusahaan'
                                              : null,
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Kapal Card
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Pilih Kapal',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(height: 12),
                              _isLoadingShips
                                  ? const Center(
                                    child: CircularProgressIndicator(),
                                  )
                                  : DropdownButtonFormField<int>(
                                    value: _kapalId,
                                    decoration: const InputDecoration(
                                      labelText: 'Pilih Kapal',
                                      border: OutlineInputBorder(),
                                    ),
                                    items:
                                        _ships.map((ship) {
                                          final kategoriText =
                                              ship['kategori_nama'] != null
                                                  ? ' - ${ship['kategori_nama']}'
                                                  : ' - Tanpa Kategori';
                                          return DropdownMenuItem<int>(
                                            value: ship['id'],
                                            child: Text(
                                              '${ship['nama_kapal']} (${ship['nomor_imo']})$kategoriText',
                                            ),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _kapalId = value;
                                      });
                                      _loadTarifs();
                                    },
                                    validator:
                                        (value) =>
                                            value == null
                                                ? 'Pilih kapal'
                                                : null,
                                  ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Tarif Card
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Pilih Tarif',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey.shade800,
                                ),
                              ),
                              const SizedBox(height: 12),
                              _isLoadingTarifs
                                  ? const Center(
                                    child: CircularProgressIndicator(),
                                  )
                                  : DropdownButtonFormField<int>(
                                    value: _tarifId,
                                    decoration: const InputDecoration(
                                      labelText: 'Pilih Tipe Tarif',
                                      border: OutlineInputBorder(),
                                    ),
                                    items:
                                        _tarifs.map((tarif) {
                                          return DropdownMenuItem<int>(
                                            value: tarif['id'],
                                            child: Text(tarif['display_text']),
                                          );
                                        }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _tarifId = value;
                                        _selectedTarif = _tarifs.firstWhere(
                                          (t) => t['id'] == value,
                                        );
                                      });
                                    },
                                    validator:
                                        (value) =>
                                            value == null
                                                ? 'Pilih tarif'
                                                : null,
                                  ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Jumlah dan Perhitungan Card
                      if (_selectedTarif != null) ...[
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Perhitungan',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                TextFormField(
                                  controller: _jumlahController,
                                  decoration: InputDecoration(
                                    labelText:
                                        'Jumlah (${_selectedTarif!['satuan']})',
                                    border: const OutlineInputBorder(),
                                    suffixText: _selectedTarif!['satuan'],
                                  ),
                                  keyboardType: TextInputType.number,
                                  onChanged: (value) {
                                    setState(
                                      () {},
                                    ); // Trigger rebuild for total calculation
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Masukkan jumlah';
                                    }
                                    final jumlah = double.tryParse(value);
                                    if (jumlah == null || jumlah <= 0) {
                                      return 'Jumlah harus lebih dari 0';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.blue.shade200,
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text('Harga per satuan:'),
                                          Text(
                                            'Rp ${_formatCurrency(_selectedTarif!['harga'].toDouble())}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text('Total Bayar:'),
                                          Text(
                                            'Rp ${_formatCurrency(_totalBayar)}',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.blue.shade700,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Metode Pembayaran Card
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Metode Pembayaran',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                DropdownButtonFormField<int>(
                                  value: _metodePembayaranId,
                                  decoration: const InputDecoration(
                                    labelText: 'Pilih Metode Pembayaran',
                                    border: OutlineInputBorder(),
                                  ),
                                  items:
                                      _paymentMethods.map((method) {
                                        return DropdownMenuItem<int>(
                                          value: method['id'],
                                          child: Text(
                                            '${method['nama_metode']} (${method['tipe']})',
                                          ),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _metodePembayaranId = value;
                                    });
                                  },
                                  validator:
                                      (value) =>
                                          value == null
                                              ? 'Pilih metode pembayaran'
                                              : null,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Keterangan Card
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Keterangan (Opsional)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey.shade800,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                TextFormField(
                                  controller: _keteranganController,
                                  decoration: const InputDecoration(
                                    labelText: 'Keterangan tambahan',
                                    border: OutlineInputBorder(),
                                  ),
                                  maxLines: 3,
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Submit Button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _submitRetribusi,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue.shade600,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child:
                                _isLoading
                                    ? const SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                    )
                                    : const Text(
                                      'Buat Retribusi',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
    );
  }
}
