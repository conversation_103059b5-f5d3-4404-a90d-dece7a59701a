<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('kapals', function (Blueprint $table) {
            $table->unsignedBigInteger('kategori_kapal_id')->nullable()->after('company_id');
            $table->foreign('kategori_kapal_id')->references('id')->on('kategori_kapals')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('kapals', function (Blueprint $table) {
            $table->dropForeign(['kategori_kapal_id']);
            $table->dropColumn('kategori_kapal_id');
        });
    }
};
