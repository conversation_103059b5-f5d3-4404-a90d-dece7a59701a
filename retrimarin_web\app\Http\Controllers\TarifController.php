<?php

namespace App\Http\Controllers;

use App\Models\Tarif;
use App\Models\KategoriKapal;
use Illuminate\Http\Request;

class TarifController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Tarif::with('kategoriKapals');

        // Filter by rate type if specified
        if ($request->filled('jenis')) {
            $query->byJenis($request->jenis);
        }

        $tarifs = $query->orderBy('jenis_tarif')->orderBy('nama_tarif')->get();
        $jenisTarif = Tarif::getJenisTarif();

        return view('tarif.index', compact('tarifs', 'jenisTarif'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $jenisTarif = Tarif::getJenisTarif();
        $kategoriKapals = KategoriKapal::active()->orderBy('nama_kategori')->get();
        return view('tarif.create', compact('jenisTarif', 'kategoriKapals'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_tarif' => 'required|string|max:255',
            'jenis_tarif' => 'required|in:tambat,tambat_sementara,tambat_tetap,tambat_labuh,perbaikan_produksi,bongkar,muat,lewat',
            'satuan' => 'required|string|max:50',
            'harga' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive',
            'keterangan' => 'nullable|string',
        ]);

        Tarif::create($request->all());

        return redirect()->route('tarif.index')
            ->with('success', 'Data tarif berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Tarif $tarif)
    {
        return view('tarif.show', compact('tarif'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tarif $tarif)
    {
        $jenisTarif = Tarif::getJenisTarif();
        return view('tarif.edit', compact('tarif', 'jenisTarif'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Tarif $tarif)
    {
        $request->validate([
            'nama_tarif' => 'required|string|max:255',
            'jenis_tarif' => 'required|in:tambat,tambat_sementara,tambat_tetap,tambat_labuh,perbaikan_produksi,bongkar,muat,lewat',
            'satuan' => 'required|string|max:50',
            'harga' => 'required|numeric|min:0',
            'status' => 'required|in:active,inactive',
            'keterangan' => 'nullable|string',
        ]);

        $tarif->update($request->all());

        return redirect()->route('tarif.index')
            ->with('success', 'Data tarif berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tarif $tarif)
    {
        $tarif->delete();

        return redirect()->route('tarif.index')
            ->with('success', 'Data tarif berhasil dihapus.');
    }

    /**
     * Get units by rate type (AJAX endpoint).
     */
    public function getSatuanByJenis(Request $request)
    {
        $jenis = $request->get('jenis');
        $satuan = Tarif::getSatuanByJenis($jenis);

        return response()->json($satuan);
    }
}
