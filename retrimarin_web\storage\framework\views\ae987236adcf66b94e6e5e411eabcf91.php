<?php $__env->startSection('title', 'Konfigurasi Payment Gateway'); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Konfigurasi Payment Gateway</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Konfigurasi Payment Gateway</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo e(session('success')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo e(session('error')); ?>

                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                            <div class="mb-3 mb-sm-0">
                                <h5 class="card-title fw-semibold">Manajemen Konfigurasi Payment Gateway</h5>
                                <p class="card-subtitle mb-0">Kelola konfigurasi payment gateway seperti Midtrans untuk pembayaran retribusi
                                    dengan fitur pencarian, sorting, dan pagination. Anda dapat merujuk dokumentasi lengkap dari
                                    <a href="https://datatables.net/">DataTables</a>
                                </p>
                            </div>
                            <div>
                                <a href="<?php echo e(route('payment-gateway-config.create')); ?>" class="btn btn-primary">
                                    <i class="ti ti-plus me-2"></i>Tambah Konfigurasi
                                </a>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table id="payment_gateway_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Gateway</th>
                                        <th>Merchant ID</th>
                                        <th>Client Key</th>
                                        <th>Server Key</th>
                                        <th>Environment</th>
                                        <th>Status</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $configs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0"><?php echo e(ucfirst($config->gateway_name)); ?></h6>
                                            </td>
                                            <td><?php echo e($config->merchant_id); ?></td>
                                            <td>
                                                <code><?php echo e($config->masked_client_key); ?></code>
                                            </td>
                                            <td>
                                                <code><?php echo e($config->masked_server_key); ?></code>
                                            </td>
                                            <td>
                                                <?php if($config->is_production): ?>
                                                    <span class="badge bg-danger rounded-3 fw-semibold">Production</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning rounded-3 fw-semibold">Sandbox</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($config->is_active): ?>
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center gap-2">
                                                    <a href="<?php echo e(route('payment-gateway-config.show', $config)); ?>"
                                                        class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('payment-gateway-config.edit', $config)); ?>"
                                                        class="btn btn-outline-warning btn-sm" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('payment-gateway-config.destroy', $config)); ?>" method="POST"
                                                        class="d-inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus konfigurasi ini?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('#payment_gateway_datatable').DataTable({
                "pageLength": 25,
                "responsive": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/payment-gateway-config/index.blade.php ENDPATH**/ ?>