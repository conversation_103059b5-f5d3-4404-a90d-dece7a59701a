import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/petugas_login_response.dart';
import '../models/petugas.dart';

class PetugasApiService {
  static const String baseUrl = 'https://kapal.senusatour.com/api';

  // Singleton pattern
  static final PetugasApiService _instance = PetugasApiService._internal();
  factory PetugasApiService() => _instance;
  PetugasApiService._internal();

  String? _token;

  void setToken(String token) {
    _token = token;
  }

  String? get token => _token;

  Map<String, String> get headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_token != null) {
      headers['Authorization'] = 'Bearer $_token';
    }

    return headers;
  }

  Future<PetugasLoginResponse> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/petugas/login'),
        headers: headers,
        body: jsonEncode({'username': username, 'password': password}),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return PetugasLoginResponse.fromJson(data);
      } else {
        throw Exception(data['message'] ?? 'Login gagal');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<Petugas> getProfile() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/petugas/profile'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return Petugas.fromJson(data['data']['petugas']);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil profil');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<void> logout() async {
    try {
      await http.post(Uri.parse('$baseUrl/petugas/logout'), headers: headers);
      _token = null;
    } catch (e) {
      throw Exception('Logout gagal: $e');
    }
  }

  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/petugas/change-password'),
        headers: headers,
        body: jsonEncode({
          'current_password': currentPassword,
          'new_password': newPassword,
          'confirm_password': confirmPassword,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Gagal mengubah password');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<Map<String, dynamic>> updateProfile({
    required String nama,
    required String username,
    required String email,
    String? telepon,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/petugas/update-profile'),
        headers: headers,
        body: jsonEncode({
          'nama': nama,
          'username': username,
          'email': email,
          'telepon': telepon,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Gagal memperbarui profil');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  // Retribusi related endpoints
  Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/petugas/dashboard-stats'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return data['data'];
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil statistik dashboard');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getCompanies() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/petugas/companies'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(data['data']);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil data perusahaan');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getShipsByPaymentType({
    required String jenisPembayar,
    int? companyId,
  }) async {
    try {
      final queryParams = {
        'jenis_pembayar': jenisPembayar,
        if (companyId != null) 'company_id': companyId.toString(),
      };

      final uri = Uri.parse('$baseUrl/petugas/ships-by-payment-type')
          .replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: headers);

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil data kapal');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getTarifsByShipCategory({
    required int kapalId,
  }) async {
    try {
      final queryParams = {'kapal_id': kapalId.toString()};

      final uri = Uri.parse('$baseUrl/petugas/tarifs-by-ship-category')
          .replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: headers);

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil data tarif');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getPaymentMethods() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/petugas/payment-methods'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(data['data']);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil metode pembayaran');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<Map<String, dynamic>> createRetribusi({
    required String jenisPembayar,
    int? companyId,
    required int kapalId,
    required int tarifId,
    required double jumlah,
    required int metodePembayaranId,
    String? keterangan,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/petugas/retribusi'),
        headers: headers,
        body: jsonEncode({
          'jenis_pembayar': jenisPembayar,
          if (companyId != null) 'company_id': companyId,
          'kapal_id': kapalId,
          'tarif_id': tarifId,
          'jumlah': jumlah,
          'metode_pembayaran_id': metodePembayaranId,
          'keterangan': keterangan,
        }),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 || response.statusCode == 201) {
        return data;
      } else {
        throw Exception(data['message'] ?? 'Gagal membuat retribusi');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getRetribusiHistory({
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
      };

      final uri = Uri.parse('$baseUrl/petugas/retribusi-history')
          .replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: headers);

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(data['data']);
      } else {
        throw Exception(data['message'] ?? 'Gagal mengambil riwayat retribusi');
      }
    } catch (e) {
      throw Exception('Koneksi gagal: $e');
    }
  }
}
