<?php $__env->startSection('title', 'Master Tarif - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Master Tarif</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Master Tarif</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Manajemen Master Tarif</h5>
                            <p class="card-subtitle mb-0">DataTables untuk mengelola tarif berbagai jenis layanan pelabuhan
                                dengan fitur pencarian, sorting, dan pagination. Anda dapat merujuk dokumentasi lengkap dari
                                <a href="https://datatables.net/">DataTables</a>
                            </p>
                        </div>
                        <div>
                            <a href="<?php echo e(route('tarif.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Tarif
                            </a>
                        </div>
                    </div>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Filter by Rate Type -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="filterJenis" onchange="filterByJenis()">
                                <option value="">Semua Jenis Tarif</option>
                                <?php $__currentLoopData = $jenisTarif; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(request('jenis') == $key ? 'selected' : ''); ?>>
                                        <?php echo e($label); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="tarif_datatable" class="table border table-striped table-bordered text-nowrap">
                            <thead>
                                <tr>
                                    <th>Nama Tarif</th>
                                    <th>Jenis</th>
                                    <th>Kategori Kapal</th>
                                    <th>Satuan</th>
                                    <th>Harga</th>
                                    <th>Status</th>
                                    <th style="width: 120px;">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $tarifs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tarif): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <h6 class="fw-semibold mb-0"><?php echo e($tarif->nama_tarif); ?></h6>
                                        </td>
                                        <td>
                                            <span
                                                class="badge
                                            <?php if($tarif->jenis_tarif == 'tambat'): ?> bg-primary
                                            <?php elseif($tarif->jenis_tarif == 'tambat_sementara'): ?> bg-success
                                            <?php elseif($tarif->jenis_tarif == 'tambat_tetap'): ?> bg-info
                                            <?php elseif($tarif->jenis_tarif == 'tambat_labuh'): ?> bg-warning
                                            <?php elseif($tarif->jenis_tarif == 'perbaikan_produksi'): ?> bg-dark
                                            <?php elseif($tarif->jenis_tarif == 'bongkar'): ?> bg-danger
                                            <?php elseif($tarif->jenis_tarif == 'muat'): ?> bg-info
                                            <?php else: ?> bg-secondary <?php endif; ?> rounded-3 fw-semibold">
                                                <?php echo e($tarif->jenis_tarif_label); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($tarif->kategoriKapals->count() > 0): ?>
                                                <?php $__currentLoopData = $tarif->kategoriKapals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kategori): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span
                                                        class="badge bg-info rounded-3 fw-semibold me-1 mb-1"><?php echo e($kategori->nama_kategori); ?></span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <span class="text-muted">Semua Kategori</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($tarif->satuan); ?></td>
                                        <td><?php echo e($tarif->formatted_harga); ?></td>
                                        <td>
                                            <?php if($tarif->status == 'active'): ?>
                                                <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-2">
                                                <a href="<?php echo e(route('tarif.show', $tarif)); ?>"
                                                    class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('tarif.edit', $tarif)); ?>"
                                                    class="btn btn-outline-warning btn-sm" title="Edit">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('tarif.destroy', $tarif)); ?>" method="POST"
                                                    class="d-inline"
                                                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus tarif ini?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        title="Hapus">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('#tarif_datatable').DataTable({
                "pageLength": 10,
                "lengthMenu": [5, 10, 25, 50, 100],
                "scrollX": true,
                "autoWidth": false,
                "language": {
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data yang tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "search": "Cari:",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                },
                "columnDefs": [{
                    "targets": [5], // Kolom Aksi
                    "orderable": false,
                    "searchable": false,
                    "width": "120px"
                }],
                "order": [
                    [1, 'asc'],
                    [0, 'asc']
                ] // Default sort by jenis then nama
            });
        });

        function filterByJenis() {
            const jenis = document.getElementById('filterJenis').value;
            const url = new URL(window.location);
            if (jenis) {
                url.searchParams.set('jenis', jenis);
            } else {
                url.searchParams.delete('jenis');
            }
            window.location = url;
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/tarif/index.blade.php ENDPATH**/ ?>