<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tarif extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_tarif',
        'jenis_tarif',
        'satuan',
        'harga',
        'keterangan',
        'status',
    ];

    protected $casts = [
        'harga' => 'decimal:2',
    ];

    // Constants for rate types
    const JENIS_TAMBAT = 'tambat';
    const JENIS_TAMBAT_SEMENTARA = 'tambat_sementara';
    const JENIS_TAMBAT_TETAP = 'tambat_tetap';
    const JENIS_TAMBAT_LABUH = 'tambat_labuh';
    const JENIS_PERBAIKAN_PRODUKSI = 'perbaikan_produksi';
    const JENIS_BONGKAR = 'bongkar';
    const JENIS_MUAT = 'muat';
    const JENIS_LEWAT = 'lewat';

    // Get all rate types
    public static function getJenisTarif()
    {
        return [
            self::JENIS_TAMBAT => 'Tambat',
            self::JENIS_TAMBAT_SEMENTARA => 'Tambat Sementara',
            self::JENIS_TAMBAT_TETAP => 'Tambat Tetap',
            self::JENIS_TAMBAT_LABUH => 'Tambat Labuh',
            self::JENIS_PERBAIKAN_PRODUKSI => 'Perbaikan & Produksi',
            self::JENIS_BONGKAR => 'Bongkar',
            self::JENIS_MUAT => 'Muat',
            self::JENIS_LEWAT => 'Lewat',
        ];
    }

    // Get common units for each rate type
    public static function getSatuanByJenis($jenis)
    {
        $satuan = [
            self::JENIS_TAMBAT => ['jam', 'hari', 'minggu', 'bulan'],
            self::JENIS_TAMBAT_SEMENTARA => ['jam', 'hari', 'minggu'],
            self::JENIS_TAMBAT_TETAP => ['bulan', 'tahun'],
            self::JENIS_TAMBAT_LABUH => ['jam', 'hari'],
            self::JENIS_PERBAIKAN_PRODUKSI => ['jam', 'hari', 'unit'],
            self::JENIS_BONGKAR => ['kg', 'ton', 'liter', 'm3', 'unit'],
            self::JENIS_MUAT => ['kg', 'ton', 'liter', 'm3', 'unit'],
            self::JENIS_LEWAT => ['unit', 'kapal', 'trip'],
        ];

        return $satuan[$jenis] ?? [];
    }

    /**
     * Scope a query to only include active tarifs.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the ship categories that this tarif applies to.
     */
    public function kategoriKapals()
    {
        return $this->belongsToMany(KategoriKapal::class, 'tarif_kategori_kapal');
    }

    /**
     * Scope a query to filter by rate type.
     */
    public function scopeByJenis($query, $jenis)
    {
        return $query->where('jenis_tarif', $jenis);
    }

    /**
     * Get formatted price with currency.
     */
    public function getFormattedHargaAttribute()
    {
        return 'Rp ' . number_format($this->harga, 0, ',', '.');
    }

    /**
     * Get rate type label.
     */
    public function getJenisTarifLabelAttribute()
    {
        $labels = self::getJenisTarif();
        return $labels[$this->jenis_tarif] ?? $this->jenis_tarif;
    }
}
