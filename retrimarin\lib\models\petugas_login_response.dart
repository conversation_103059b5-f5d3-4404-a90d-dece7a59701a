import 'petugas.dart';

class PetugasLoginData {
  final String token;
  final Petugas petugas;

  PetugasLoginData({
    required this.token,
    required this.petugas,
  });

  factory PetugasLoginData.fromJson(Map<String, dynamic> json) {
    return PetugasLoginData(
      token: json['token'],
      petugas: Petugas.fromJson(json['petugas']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'petugas': petugas.toJson(),
    };
  }
}

class PetugasLoginResponse {
  final bool success;
  final String message;
  final PetugasLoginData data;

  PetugasLoginResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PetugasLoginResponse.fromJson(Map<String, dynamic> json) {
    return PetugasLoginResponse(
      success: json['success'],
      message: json['message'],
      data: PetugasLoginData.from<PERSON>son(json['data']),
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'success': success,
      'message': message,
      'data': data.toJson(),
    };
  }
}
