<?php $__env->startSection('title', 'Data Kapal - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Data Kapal</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Data Kapal</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Manajemen Data Kapal</h5>
                            <p class="card-subtitle mb-0">DataTables untuk mengelola semua data kapal dan informasi operator
                                dengan fitur pencarian, sorting, dan pagination. Anda dapat merujuk dokumentasi lengkap dari
                                <a href="https://datatables.net/">DataTables</a>
                            </p>
                        </div>
                        <div>
                            <a href="<?php echo e(route('kapal.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Kapal
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="kapal_datatable" class="table border table-striped table-bordered text-nowrap">
                            <thead>
                                <tr>
                                    <th>Nama Kapal</th>
                                    <th>Perusahaan</th>
                                    <th>Kategori</th>
                                    <th>IMO</th>
                                    <th>Jenis</th>
                                    <th>Bendera</th>
                                    <th>Tahun</th>
                                    <th>Username Operator</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $kapals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $kapal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <h6 class="fw-semibold mb-0"><?php echo e($kapal->nama_kapal); ?></h6>
                                        </td>
                                        <td><?php echo e($kapal->company ? $kapal->company->nama_perusahaan : 'Milik Pribadi'); ?></td>
                                        <td>
                                            <?php if($kapal->kategoriKapal): ?>
                                                <span
                                                    class="badge bg-info rounded-3 fw-semibold"><?php echo e($kapal->kategoriKapal->nama_kategori); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($kapal->nomor_imo); ?></td>
                                        <td><?php echo e($kapal->jenis_kapal); ?></td>
                                        <td><?php echo e($kapal->bendera); ?></td>
                                        <td><?php echo e($kapal->tahun_pembuatan); ?></td>
                                        <td><?php echo e($kapal->operator_username); ?></td>
                                        <td>
                                            <?php if($kapal->status == 'active'): ?>
                                                <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                            <?php elseif($kapal->status == 'maintenance'): ?>
                                                <span class="badge bg-warning rounded-3 fw-semibold">Maintenance</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center gap-2">
                                                <a href="<?php echo e(route('kapal.show', $kapal)); ?>"
                                                    class="btn btn-outline-primary btn-sm" title="Lihat Detail">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('kapal.edit', $kapal)); ?>"
                                                    class="btn btn-outline-warning btn-sm" title="Edit">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('kapal.destroy', $kapal)); ?>" method="POST"
                                                    class="d-inline"
                                                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus data kapal ini?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="btn btn-outline-danger btn-sm"
                                                        title="Hapus">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('#kapal_datatable').DataTable({
                "pageLength": 10,
                "lengthMenu": [5, 10, 25, 50, 100],
                "language": {
                    "lengthMenu": "Tampilkan _MENU_ data per halaman",
                    "zeroRecords": "Data tidak ditemukan",
                    "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                    "infoEmpty": "Tidak ada data yang tersedia",
                    "infoFiltered": "(difilter dari _MAX_ total data)",
                    "search": "Cari:",
                    "paginate": {
                        "first": "Pertama",
                        "last": "Terakhir",
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    }
                },
                "columnDefs": [{
                    "targets": [8], // Kolom Aksi
                    "orderable": false,
                    "searchable": false
                }],
                "order": [
                    [0, 'asc']
                ] // Default sort by nama kapal
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/kapal/index.blade.php ENDPATH**/ ?>