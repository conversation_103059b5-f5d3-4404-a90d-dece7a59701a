@extends('layouts.app')

@section('title', 'Edit Tarif - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Edit Tarif</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('tarif.index') }}">Master
                                    Tarif</a></li>
                            <li class="breadcrumb-item" aria-current="page">Edit Tarif</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Form Edit Tarif: {{ $tarif->nama_tarif }}</h5>
                            <p class="card-subtitle mb-0">Perbarui informasi tarif</p>
                        </div>
                        <div>
                            <a href="{{ route('tarif.show', $tarif) }}" class="btn btn-outline-info me-2">
                                <i class="ti ti-eye me-2"></i>Lihat Detail
                            </a>
                            <a href="{{ route('tarif.index') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>Kembali
                            </a>
                        </div>
                    </div>

                    <form action="{{ route('tarif.update', $tarif) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Informasi Tarif</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="nama_tarif" class="form-label">Nama Tarif <span
                                                            class="text-danger">*</span></label>
                                                    <input type="text"
                                                        class="form-control @error('nama_tarif') is-invalid @enderror"
                                                        id="nama_tarif" name="nama_tarif"
                                                        value="{{ old('nama_tarif', $tarif->nama_tarif) }}" required>
                                                    @error('nama_tarif')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="jenis_tarif" class="form-label">Jenis Tarif <span
                                                            class="text-danger">*</span></label>
                                                    <select class="form-select @error('jenis_tarif') is-invalid @enderror"
                                                        id="jenis_tarif" name="jenis_tarif" required
                                                        onchange="updateSatuanOptions()">
                                                        <option value="">Pilih Jenis Tarif</option>
                                                        @foreach ($jenisTarif as $key => $label)
                                                            <option value="{{ $key }}"
                                                                {{ old('jenis_tarif', $tarif->jenis_tarif) == $key ? 'selected' : '' }}>
                                                                {{ $label }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('jenis_tarif')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="satuan" class="form-label">Satuan <span
                                                            class="text-danger">*</span></label>
                                                    <select class="form-select @error('satuan') is-invalid @enderror"
                                                        id="satuan" name="satuan" required>
                                                        <option value="">Pilih Satuan</option>
                                                    </select>
                                                    @error('satuan')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    <div class="form-text">Atau ketik satuan custom jika tidak ada dalam
                                                        pilihan</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="harga" class="form-label">Harga <span
                                                            class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number"
                                                            class="form-control @error('harga') is-invalid @enderror"
                                                            id="harga" name="harga"
                                                            value="{{ old('harga', $tarif->harga) }}" min="0"
                                                            step="0.01" required>
                                                        @error('harga')
                                                            <div class="invalid-feedback">{{ $message }}</div>
                                                        @enderror
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="kategori_kapal_ids" class="form-label">Kategori Kapal</label>
                                            <select class="form-select @error('kategori_kapal_ids') is-invalid @enderror"
                                                id="kategori_kapal_ids" name="kategori_kapal_ids[]" multiple>
                                                @foreach ($kategoriKapals as $kategori)
                                                    <option value="{{ $kategori->id }}"
                                                        {{ in_array($kategori->id, old('kategori_kapal_ids', $tarif->kategoriKapals->pluck('id')->toArray())) ? 'selected' : '' }}>
                                                        {{ $kategori->nama_kategori }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('kategori_kapal_ids')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Pilih kategori kapal yang berlaku untuk tarif ini.
                                                Kosongkan jika berlaku untuk semua kategori.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="keterangan" class="form-label">Keterangan</label>
                                            <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan"
                                                rows="3">{{ old('keterangan', $tarif->keterangan) }}</textarea>
                                            @error('keterangan')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Status</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                <option value="active"
                                                    {{ old('status', $tarif->status) == 'active' ? 'selected' : '' }}>Aktif
                                                </option>
                                                <option value="inactive"
                                                    {{ old('status', $tarif->status) == 'inactive' ? 'selected' : '' }}>
                                                    Tidak Aktif</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Rate Type Information -->
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">Jenis Tarif:</h6>
                                            <ul class="mb-0 small">
                                                <li><strong>Parkir:</strong> Tarif untuk parkir kapal (per jam/hari)</li>
                                                <li><strong>Bongkar:</strong> Tarif untuk bongkar muatan (per kg/ton/unit)
                                                </li>
                                                <li><strong>Muat:</strong> Tarif untuk muat muatan (per kg/ton/unit)</li>
                                                <li><strong>Lewat:</strong> Tarif untuk kapal yang lewat (per unit/trip)
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-device-floppy me-2"></i>Simpan Perubahan
                                    </button>
                                    <a href="{{ route('tarif.index') }}" class="btn btn-outline-secondary">
                                        <i class="ti ti-x me-2"></i>Batal
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            // Initialize Select2 for kategori kapal
            $('#kategori_kapal_ids').select2({
                theme: 'bootstrap-5',
                placeholder: 'Pilih kategori kapal (opsional)',
                allowClear: true,
                width: '100%'
            });
        });

        // Satuan options for each rate type
        const satuanOptions = {
            'tambat': ['jam', 'hari', 'minggu', 'bulan'],
            'tambat_sementara': ['jam', 'hari', 'minggu', 'bulan'],
            'tambat_tetap': ['hari', 'minggu', 'bulan', 'tahun'],
            'tambat_labuh': ['jam', 'hari', 'minggu', 'bulan'],
            'perbaikan_produksi': ['jam', 'hari', 'minggu', 'bulan'],
            'bongkar': ['kg', 'ton', 'liter', 'm3', 'unit'],
            'muat': ['kg', 'ton', 'liter', 'm3', 'unit'],
            'lewat': ['unit', 'kapal', 'trip']
        };

        const currentSatuan = '{{ old('satuan', $tarif->satuan) }}';

        function updateSatuanOptions() {
            const jenisSelect = document.getElementById('jenis_tarif');
            const satuanSelect = document.getElementById('satuan');
            const selectedJenis = jenisSelect.value;

            // Clear existing options
            satuanSelect.innerHTML = '<option value="">Pilih Satuan</option>';

            if (selectedJenis && satuanOptions[selectedJenis]) {
                satuanOptions[selectedJenis].forEach(function(satuan) {
                    const option = document.createElement('option');
                    option.value = satuan;
                    option.textContent = satuan;
                    if (satuan === currentSatuan) {
                        option.selected = true;
                    }
                    satuanSelect.appendChild(option);
                });

                // If current satuan is not in the predefined options, add it as custom
                if (!satuanOptions[selectedJenis].includes(currentSatuan) && currentSatuan) {
                    const customOption = document.createElement('option');
                    customOption.value = currentSatuan;
                    customOption.textContent = currentSatuan + ' (custom)';
                    customOption.selected = true;
                    satuanSelect.appendChild(customOption);
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateSatuanOptions();
        });
    </script>
@endpush
