@extends('layouts.app')

@section('title', '<PERSON> - ' . config('app.name'))

@section('content')
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Edit Data Kapal</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item"><a class="text-muted" href="{{ route('kapal.index') }}">Data
                                    Kapal</a></li>
                            <li class="breadcrumb-item" aria-current="page">Edit <PERSON></li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Form Edit Data Kapal: {{ $kapal->nama_kapal }}</h5>
                            <p class="card-subtitle mb-0">Perbarui informasi kapal dan data operator</p>
                        </div>
                        <div>
                            <a href="{{ route('kapal.show', $kapal) }}" class="btn btn-outline-info me-2">
                                <i class="ti ti-eye me-2"></i>Lihat Detail
                            </a>
                            <a href="{{ route('kapal.index') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>Kembali
                            </a>
                        </div>
                    </div>

                    <form action="{{ route('kapal.update', $kapal) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Informasi Kapal -->
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Informasi Kapal</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="company_id" class="form-label">Perusahaan</label>
                                            <select class="form-select @error('company_id') is-invalid @enderror"
                                                id="company_id" name="company_id">
                                                <option value="">Pilih Perusahaan (Opsional)</option>
                                                @foreach ($companies as $company)
                                                    <option value="{{ $company->id }}"
                                                        {{ old('company_id', $kapal->company_id) == $company->id ? 'selected' : '' }}>
                                                        {{ $company->nama_perusahaan }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('company_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Kosongkan jika kapal milik pribadi</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="kategori_kapal_id" class="form-label">Kategori Kapal</label>
                                            <select class="form-select @error('kategori_kapal_id') is-invalid @enderror"
                                                id="kategori_kapal_id" name="kategori_kapal_id">
                                                <option value="">Pilih Kategori Kapal (Opsional)</option>
                                                @foreach ($kategoriKapals as $kategori)
                                                    <option value="{{ $kategori->id }}"
                                                        {{ old('kategori_kapal_id', $kapal->kategori_kapal_id) == $kategori->id ? 'selected' : '' }}>
                                                        {{ $kategori->nama_kategori }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('kategori_kapal_id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Pilih kategori kapal seperti Besar, Kecil, Sedang, dll
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="nama_kapal" class="form-label">Nama Kapal <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('nama_kapal') is-invalid @enderror"
                                                id="nama_kapal" name="nama_kapal"
                                                value="{{ old('nama_kapal', $kapal->nama_kapal) }}" required>
                                            @error('nama_kapal')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="nomor_imo" class="form-label">Nomor IMO <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('nomor_imo') is-invalid @enderror" id="nomor_imo"
                                                name="nomor_imo" value="{{ old('nomor_imo', $kapal->nomor_imo) }}"
                                                required>
                                            @error('nomor_imo')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="jenis_kapal" class="form-label">Jenis Kapal <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select @error('jenis_kapal') is-invalid @enderror"
                                                id="jenis_kapal" name="jenis_kapal" required>
                                                <option value="">Pilih Jenis Kapal</option>
                                                <option value="Cargo Ship"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Cargo Ship' ? 'selected' : '' }}>
                                                    Cargo Ship</option>
                                                <option value="Container Ship"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Container Ship' ? 'selected' : '' }}>
                                                    Container Ship</option>
                                                <option value="Tanker"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Tanker' ? 'selected' : '' }}>
                                                    Tanker</option>
                                                <option value="Bulk Carrier"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Bulk Carrier' ? 'selected' : '' }}>
                                                    Bulk Carrier</option>
                                                <option value="Passenger Ship"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Passenger Ship' ? 'selected' : '' }}>
                                                    Passenger Ship</option>
                                                <option value="Fishing Vessel"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Fishing Vessel' ? 'selected' : '' }}>
                                                    Fishing Vessel</option>
                                                <option value="Other"
                                                    {{ old('jenis_kapal', $kapal->jenis_kapal) == 'Other' ? 'selected' : '' }}>
                                                    Lainnya</option>
                                            </select>
                                            @error('jenis_kapal')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="bendera" class="form-label">Bendera <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('bendera') is-invalid @enderror" id="bendera"
                                                name="bendera" value="{{ old('bendera', $kapal->bendera) }}" required>
                                            @error('bendera')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="tahun_pembuatan" class="form-label">Tahun Pembuatan <span
                                                    class="text-danger">*</span></label>
                                            <input type="number"
                                                class="form-control @error('tahun_pembuatan') is-invalid @enderror"
                                                id="tahun_pembuatan" name="tahun_pembuatan"
                                                value="{{ old('tahun_pembuatan', $kapal->tahun_pembuatan) }}"
                                                min="1900" max="{{ date('Y') }}" required>
                                            @error('tahun_pembuatan')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="panjang" class="form-label">Panjang (m) <span
                                                            class="text-danger">*</span></label>
                                                    <input type="number" step="0.01"
                                                        class="form-control @error('panjang') is-invalid @enderror"
                                                        id="panjang" name="panjang"
                                                        value="{{ old('panjang', $kapal->panjang) }}" required>
                                                    @error('panjang')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="lebar" class="form-label">Lebar (m) <span
                                                            class="text-danger">*</span></label>
                                                    <input type="number" step="0.01"
                                                        class="form-control @error('lebar') is-invalid @enderror"
                                                        id="lebar" name="lebar"
                                                        value="{{ old('lebar', $kapal->lebar) }}" required>
                                                    @error('lebar')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="draft" class="form-label">Draft (m) <span
                                                            class="text-danger">*</span></label>
                                                    <input type="number" step="0.01"
                                                        class="form-control @error('draft') is-invalid @enderror"
                                                        id="draft" name="draft"
                                                        value="{{ old('draft', $kapal->draft) }}" required>
                                                    @error('draft')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="gross_tonnage" class="form-label">Gross Tonnage <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" step="0.01"
                                                class="form-control @error('gross_tonnage') is-invalid @enderror"
                                                id="gross_tonnage" name="gross_tonnage"
                                                value="{{ old('gross_tonnage', $kapal->gross_tonnage) }}" required>
                                            @error('gross_tonnage')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Informasi Operator -->
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Informasi Operator</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="operator_username" class="form-label">Username Operator <span
                                                    class="text-danger">*</span></label>
                                            <input type="text"
                                                class="form-control @error('operator_username') is-invalid @enderror"
                                                id="operator_username" name="operator_username"
                                                value="{{ old('operator_username', $kapal->operator_username) }}"
                                                required>
                                            @error('operator_username')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Username untuk login operator kapal</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="operator_password" class="form-label">Password Operator</label>
                                            <input type="password"
                                                class="form-control @error('operator_password') is-invalid @enderror"
                                                id="operator_password" name="operator_password">
                                            @error('operator_password')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Kosongkan jika tidak ingin mengubah password. Minimal 6
                                                karakter jika diisi.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span
                                                    class="text-danger">*</span></label>
                                            <select class="form-select @error('status') is-invalid @enderror"
                                                id="status" name="status" required>
                                                <option value="">Pilih Status</option>
                                                <option value="active"
                                                    {{ old('status', $kapal->status) == 'active' ? 'selected' : '' }}>Aktif
                                                </option>
                                                <option value="inactive"
                                                    {{ old('status', $kapal->status) == 'inactive' ? 'selected' : '' }}>
                                                    Tidak Aktif</option>
                                                <option value="maintenance"
                                                    {{ old('status', $kapal->status) == 'maintenance' ? 'selected' : '' }}>
                                                    Maintenance</option>
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="keterangan" class="form-label">Keterangan</label>
                                            <textarea class="form-control @error('keterangan') is-invalid @enderror" id="keterangan" name="keterangan"
                                                rows="4">{{ old('keterangan', $kapal->keterangan) }}</textarea>
                                            @error('keterangan')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-device-floppy me-2"></i>Update Data
                                    </button>
                                    <a href="{{ route('kapal.show', $kapal) }}" class="btn btn-outline-info">
                                        <i class="ti ti-eye me-2"></i>Lihat Detail
                                    </a>
                                    <a href="{{ route('kapal.index') }}" class="btn btn-outline-secondary">
                                        <i class="ti ti-x me-2"></i>Batal
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
