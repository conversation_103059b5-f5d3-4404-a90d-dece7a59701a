@extends('layouts.app')

@section('title', 'Edit Konfigurasi Payment Gateway')

@section('content')
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Edit Konfigurasi Payment Gateway</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('payment-gateway-config.index') }}">Konfigurasi Payment Gateway</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5 class="card-title mb-0">Form Edit Konfigurasi Payment Gateway</h5>
                            <a href="{{ route('payment-gateway-config.index') }}" class="btn btn-outline-secondary">
                                <i class="ti ti-arrow-left me-2"></i>Kembali
                            </a>
                        </div>
                    </div>

                    <form action="{{ route('payment-gateway-config.update', $paymentGatewayConfig) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Informasi Gateway</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="gateway_name" class="form-label">Nama Gateway <span
                                                            class="text-danger">*</span></label>
                                                    <select class="form-select @error('gateway_name') is-invalid @enderror"
                                                        id="gateway_name" name="gateway_name" required>
                                                        <option value="">Pilih Gateway</option>
                                                        <option value="midtrans" {{ old('gateway_name', $paymentGatewayConfig->gateway_name) == 'midtrans' ? 'selected' : '' }}>
                                                            Midtrans</option>
                                                    </select>
                                                    @error('gateway_name')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="merchant_id" class="form-label">Merchant ID <span
                                                            class="text-danger">*</span></label>
                                                    <input type="text"
                                                        class="form-control @error('merchant_id') is-invalid @enderror"
                                                        id="merchant_id" name="merchant_id"
                                                        value="{{ old('merchant_id', $paymentGatewayConfig->merchant_id) }}" required
                                                        placeholder="Masukkan Merchant ID">
                                                    @error('merchant_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="client_key" class="form-label">Client Key <span
                                                            class="text-danger">*</span></label>
                                                    <input type="text"
                                                        class="form-control @error('client_key') is-invalid @enderror"
                                                        id="client_key" name="client_key"
                                                        value="{{ old('client_key', $paymentGatewayConfig->client_key) }}" required
                                                        placeholder="Masukkan Client Key">
                                                    @error('client_key')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="server_key" class="form-label">Server Key <span
                                                            class="text-danger">*</span></label>
                                                    <input type="password"
                                                        class="form-control @error('server_key') is-invalid @enderror"
                                                        id="server_key" name="server_key"
                                                        value="{{ old('server_key', $paymentGatewayConfig->server_key) }}" required
                                                        placeholder="Masukkan Server Key">
                                                    @error('server_key')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="mb-3">
                                                    <label for="description" class="form-label">Deskripsi</label>
                                                    <textarea class="form-control @error('description') is-invalid @enderror"
                                                        id="description" name="description" rows="3"
                                                        placeholder="Deskripsi konfigurasi (opsional)">{{ old('description', $paymentGatewayConfig->description) }}</textarea>
                                                    @error('description')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Pengaturan</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is_production" 
                                                    name="is_production" value="1" {{ old('is_production', $paymentGatewayConfig->is_production) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_production">
                                                    Mode Production
                                                </label>
                                            </div>
                                            <div class="form-text">Aktifkan untuk menggunakan environment production</div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="is_active" 
                                                    name="is_active" value="1" {{ old('is_active', $paymentGatewayConfig->is_active) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="is_active">
                                                    Status Aktif
                                                </label>
                                            </div>
                                            <div class="form-text">Aktifkan untuk menggunakan konfigurasi ini</div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="ti ti-device-floppy me-2"></i>Update
                                            </button>
                                            <a href="{{ route('payment-gateway-config.index') }}" class="btn btn-outline-secondary">
                                                <i class="ti ti-x me-2"></i>Batal
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
