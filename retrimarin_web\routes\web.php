<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\KapalController;
use App\Http\Controllers\KategoriKapalController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\TarifController;
use App\Http\Controllers\PetugasController;
use App\Http\Controllers\PetugasAuthController;
use App\Http\Controllers\PetugasDashboardController;
use App\Http\Controllers\MetodePembayaranController;
use App\Http\Controllers\PaymentGatewayConfigController;
use App\Http\Controllers\WilayahController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\AdminRetribusiController;

Route::get('/', function () {
    return redirect()->route('login');
});

// Dashboard routes (protected by auth middleware)
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Company management routes
    Route::resource('company', CompanyController::class);

    // Kategori Kapal management routes
    Route::resource('kategori-kapal', KategoriKapalController::class);

    // Kapal management routes
    Route::resource('kapal', KapalController::class);

    // Tarif management routes
    Route::resource('tarif', TarifController::class);
    Route::get('tarif/satuan/{jenis}', [TarifController::class, 'getSatuanByJenis'])->name('tarif.satuan');

    // Petugas management routes (Admin)
    Route::resource('petugas', PetugasController::class)->parameters([
        'petugas' => 'petugas'
    ]);

    // Metode Pembayaran management routes
    Route::resource('metode-pembayaran', MetodePembayaranController::class);

    // Wilayah management routes
    Route::resource('wilayah', WilayahController::class);
    Route::get('wilayah-map', [WilayahController::class, 'map'])->name('wilayah.map');

    // Payment Gateway Configuration routes
    Route::resource('payment-gateway-config', PaymentGatewayConfigController::class);

    // Tracking routes
    Route::prefix('tracking')->name('tracking.')->group(function () {
        Route::get('/', [TrackingController::class, 'index'])->name('index');
        Route::get('/map', [TrackingController::class, 'map'])->name('map');
        Route::get('/export', [TrackingController::class, 'export'])->name('export');
        Route::get('/api/data', [TrackingController::class, 'apiData'])->name('api.data');
        Route::get('/{id}', [TrackingController::class, 'show'])->name('show');
    });

    // Admin Retribusi Monitoring routes
    Route::prefix('admin/retribusi')->name('admin.retribusi.')->group(function () {
        Route::get('/', [AdminRetribusiController::class, 'index'])->name('index');
        Route::get('/statistics', [AdminRetribusiController::class, 'getStatistics'])->name('statistics');
        Route::get('/export', [AdminRetribusiController::class, 'export'])->name('export');
        Route::get('/{retribusi}', [AdminRetribusiController::class, 'show'])->name('show');
    });
});

// Petugas authentication routes
Route::prefix('petugas-login')->name('petugas.')->group(function () {
    Route::get('/', [PetugasAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/', [PetugasAuthController::class, 'login']);
    Route::post('logout', [PetugasAuthController::class, 'logout'])->name('logout');
});

// Petugas dashboard routes (protected by petugas auth middleware)
Route::middleware(['auth.petugas'])->prefix('petugas-dashboard')->name('petugas.')->group(function () {
    Route::get('/', [PetugasDashboardController::class, 'index'])->name('dashboard');
    Route::get('retribusi', [PetugasDashboardController::class, 'listRetribusi'])->name('retribusi.index');
    Route::get('retribusi/create', [PetugasDashboardController::class, 'createRetribusi'])->name('retribusi.create');
    Route::post('retribusi', [PetugasDashboardController::class, 'storeRetribusi'])->name('retribusi.store');
    Route::get('retribusi/{retribusi}', [PetugasDashboardController::class, 'showRetribusi'])->name('retribusi.show');
    Route::patch('retribusi/{retribusi}/lunas', [PetugasDashboardController::class, 'markAsLunas'])->name('retribusi.lunas');
    Route::patch('retribusi/{retribusi}/cancel', [PetugasDashboardController::class, 'cancelRetribusi'])->name('retribusi.cancel');
    Route::get('tarif-details', [PetugasDashboardController::class, 'getTarifDetails'])->name('tarif.details');
    Route::get('ships-by-payment-type', [PetugasDashboardController::class, 'getShipsByPaymentType'])->name('ships.by.payment.type');
});

// require __DIR__ . '/auth.php';
