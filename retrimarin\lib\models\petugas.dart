class Petugas {
  final int id;
  final String nama;
  final String username;
  final String email;
  final String? telepon;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Petugas({
    required this.id,
    required this.nama,
    required this.username,
    required this.email,
    this.telepon,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Petugas.fromJson(Map<String, dynamic> json) {
    return Petugas(
      id: json['id'],
      nama: json['nama'],
      username: json['username'],
      email: json['email'],
      telepon: json['telepon'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nama': nama,
      'username': username,
      'email': email,
      'telepon': telepon,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isActive => status == 'active';
}
