@extends('layouts.app')

@section('title', 'Detail Konfigurasi Payment Gateway')

@section('content')
    <div class="container-fluid">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Konfigurasi Payment Gateway</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('dashboard') }}">Dashboard</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted" href="{{ route('payment-gateway-config.index') }}">Konfigurasi Payment Gateway</a>
                                </li>
                                <li class="breadcrumb-item" aria-current="page">Detail</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5 class="card-title mb-0">Informasi Konfigurasi Payment Gateway</h5>
                            <div>
                                <a href="{{ route('payment-gateway-config.edit', $paymentGatewayConfig) }}" class="btn btn-warning btn-sm">
                                    <i class="ti ti-edit me-1"></i>Edit
                                </a>
                                <a href="{{ route('payment-gateway-config.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="ti ti-arrow-left me-1"></i>Kembali
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Nama Gateway</label>
                                    <p class="form-control-static">{{ ucfirst($paymentGatewayConfig->gateway_name) }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Merchant ID</label>
                                    <p class="form-control-static">{{ $paymentGatewayConfig->merchant_id }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Client Key</label>
                                    <p class="form-control-static">
                                        <code>{{ $paymentGatewayConfig->masked_client_key }}</code>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Server Key</label>
                                    <p class="form-control-static">
                                        <code>{{ $paymentGatewayConfig->masked_server_key }}</code>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Environment</label>
                                    <p class="form-control-static">
                                        @if ($paymentGatewayConfig->is_production)
                                            <span class="badge bg-danger rounded-3 fw-semibold">Production</span>
                                        @else
                                            <span class="badge bg-warning rounded-3 fw-semibold">Sandbox</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Status</label>
                                    <p class="form-control-static">
                                        @if ($paymentGatewayConfig->is_active)
                                            <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                        @else
                                            <span class="badge bg-secondary rounded-3 fw-semibold">Tidak Aktif</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Deskripsi</label>
                                    <p class="form-control-static">{{ $paymentGatewayConfig->description ?? 'Tidak ada deskripsi' }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Dibuat</label>
                                    <p class="form-control-static">{{ $paymentGatewayConfig->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Terakhir Diupdate</label>
                                    <p class="form-control-static">{{ $paymentGatewayConfig->updated_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Informasi Keamanan</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">Perhatian!</h6>
                            <p class="mb-0">Kunci API (Client Key dan Server Key) ditampilkan dalam bentuk tersamar untuk keamanan. Pastikan untuk menjaga kerahasiaan kunci API Anda.</p>
                        </div>
                        
                        @if($paymentGatewayConfig->is_production)
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Mode Production!</h6>
                            <p class="mb-0">Konfigurasi ini menggunakan mode production. Pastikan semua pengaturan sudah benar sebelum digunakan untuk transaksi nyata.</p>
                        </div>
                        @else
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Mode Sandbox</h6>
                            <p class="mb-0">Konfigurasi ini menggunakan mode sandbox untuk testing. Tidak ada transaksi nyata yang akan diproses.</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
