<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tarif_kategori_kapal', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('tarif_id');
            $table->unsignedBigInteger('kategori_kapal_id');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tarif_id')->references('id')->on('tarifs')->onDelete('cascade');
            $table->foreign('kategori_kapal_id')->references('id')->on('kategori_kapals')->onDelete('cascade');

            // Unique constraint to prevent duplicate relationships
            $table->unique(['tarif_id', 'kategori_kapal_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tarif_kategori_kapal');
    }
};
