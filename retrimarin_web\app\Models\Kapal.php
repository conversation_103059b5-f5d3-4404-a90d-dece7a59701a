<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class <PERSON>pal extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'company_id',
        'kategori_kapal_id',
        'nama_kapal',
        'nomor_imo',
        'jenis_kapal',
        'bendera',
        'tahun_pembuatan',
        'panjang',
        'lebar',
        'draft',
        'gross_tonnage',
        'operator_username',
        'operator_password',
        'status',
        'keterangan',
    ];

    protected $hidden = [
        'operator_password',
    ];

    protected $casts = [
        'panjang' => 'decimal:2',
        'lebar' => 'decimal:2',
        'draft' => 'decimal:2',
        'gross_tonnage' => 'decimal:2',
        'operator_password' => 'hashed',
    ];

    /**
     * Get the username field for authentication.
     */
    public function getAuthIdentifierName()
    {
        return 'operator_username';
    }

    /**
     * Get the password field for authentication.
     */
    public function getAuthPassword()
    {
        return $this->operator_password;
    }

    /**
     * Scope a query to only include active ships.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Get the company that owns the ship.
     */
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the category that the ship belongs to.
     */
    public function kategoriKapal()
    {
        return $this->belongsTo(KategoriKapal::class);
    }
}
