<?php

namespace App\Http\Controllers;

use App\Models\PaymentGatewayConfig;
use Illuminate\Http\Request;

class PaymentGatewayConfigController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $configs = PaymentGatewayConfig::orderBy('gateway_name')->get();
        return view('payment-gateway-config.index', compact('configs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('payment-gateway-config.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'gateway_name' => 'required|string|max:255',
            'client_key' => 'required|string|max:255',
            'server_key' => 'required|string|max:255',
            'merchant_id' => 'required|string|max:255',
            'is_production' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        PaymentGatewayConfig::create($request->all());

        return redirect()->route('payment-gateway-config.index')
            ->with('success', 'Konfigurasi payment gateway berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PaymentGatewayConfig $paymentGatewayConfig)
    {
        return view('payment-gateway-config.show', compact('paymentGatewayConfig'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PaymentGatewayConfig $paymentGatewayConfig)
    {
        return view('payment-gateway-config.edit', compact('paymentGatewayConfig'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PaymentGatewayConfig $paymentGatewayConfig)
    {
        $request->validate([
            'gateway_name' => 'required|string|max:255',
            'client_key' => 'required|string|max:255',
            'server_key' => 'required|string|max:255',
            'merchant_id' => 'required|string|max:255',
            'is_production' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
        ]);

        $paymentGatewayConfig->update($request->all());

        return redirect()->route('payment-gateway-config.index')
            ->with('success', 'Konfigurasi payment gateway berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PaymentGatewayConfig $paymentGatewayConfig)
    {
        $paymentGatewayConfig->delete();

        return redirect()->route('payment-gateway-config.index')
            ->with('success', 'Konfigurasi payment gateway berhasil dihapus.');
    }
}
