import 'package:flutter/foundation.dart';
import '../models/petugas.dart';
import '../services/petugas_auth_service.dart';

class PetugasAuthProvider with ChangeNotifier {
  final PetugasAuthService _authService = PetugasAuthService();

  Petugas? _petugas;
  bool _isLoading = false;
  String? _errorMessage;

  Petugas? get petugas => _petugas;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _petugas != null;

  Future<void> checkAuthStatus() async {
    _setLoading(true);
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        _petugas = await _authService.getPetugas();
      }
    } catch (e) {
      _setError('Gagal memeriksa status login');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _authService.login(username, password);
      if (success) {
        _petugas = await _authService.getPetugas();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);
    try {
      await _authService.logout();
      _petugas = null;
      _clearError();

      // Force UI update with explicit notify
      notifyListeners();

      // Additional notify after microtask to ensure UI updates
      Future.microtask(() {
        notifyListeners();
      });
    } catch (e) {
      _setError('Logout gagal');
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
