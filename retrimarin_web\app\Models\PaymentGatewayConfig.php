<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentGatewayConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'gateway_name',
        'client_key',
        'server_key',
        'merchant_id',
        'is_production',
        'is_active',
        'description',
    ];

    protected $casts = [
        'is_production' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active configs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by gateway name.
     */
    public function scopeByGateway($query, $gateway)
    {
        return $query->where('gateway_name', $gateway);
    }

    /**
     * Get the environment label.
     */
    public function getEnvironmentLabelAttribute()
    {
        return $this->is_production ? 'Production' : 'Sandbox';
    }

    /**
     * Get masked server key for display.
     */
    public function getMaskedServerKeyAttribute()
    {
        return substr($this->server_key, 0, 8) . str_repeat('*', strlen($this->server_key) - 8);
    }

    /**
     * Get masked client key for display.
     */
    public function getMaskedClientKeyAttribute()
    {
        return substr($this->client_key, 0, 8) . str_repeat('*', strlen($this->client_key) - 8);
    }
}
