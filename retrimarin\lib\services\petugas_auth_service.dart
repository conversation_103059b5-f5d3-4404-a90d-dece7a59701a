import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/petugas.dart';
import 'petugas_api_service.dart';

class PetugasAuthService {
  static const String _tokenKey = 'petugas_token';
  static const String _petugasKey = 'petugas_data';

  final PetugasApiService _apiService = PetugasApiService();

  Future<bool> login(String username, String password) async {
    try {
      final response = await _apiService.login(username, password);

      if (response.success) {
        // Save token and petugas data
        await _saveToken(response.data.token);
        await _savePetugas(response.data.petugas);

        // Set token to API service
        _apiService.setToken(response.data.token);

        return true;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await _apiService.logout();
    } catch (e) {
      // Continue with local logout even if API call fails
    } finally {
      await _clearStorage();
    }
  }

  Future<bool> isLoggedIn() async {
    final token = await getToken();
    final petugas = await getPetugas();

    if (token != null && petugas != null) {
      // Set token to API service for future requests
      _apiService.setToken(token);
      return true;
    }

    return false;
  }

  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  Future<Petugas?> getPetugas() async {
    final prefs = await SharedPreferences.getInstance();
    final petugasJson = prefs.getString(_petugasKey);
    if (petugasJson != null) {
      try {
        final Map<String, dynamic> petugasMap = jsonDecode(petugasJson);
        return Petugas.fromJson(petugasMap);
      } catch (e) {
        // If parsing fails, return null and clear storage
        await _clearStorage();
        return null;
      }
    }
    return null;
  }

  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  Future<void> _savePetugas(Petugas petugas) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_petugasKey, jsonEncode(petugas.toJson()));
  }

  Future<void> _clearStorage() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_petugasKey);
  }
}
